name: 'Start R2R Server'
description: 'Starts the R2R server'
inputs:
  config-name:
    description: 'The R2R configuration name to use'
    required: false
    default: 'r2r_azure_with_test_limits'
runs:
  using: "composite"
  steps:
    - name: Start R2R server
      shell: bash
      run: |
        cd py
        export R2R_CONFIG_NAME=${{ inputs.config-name }}
        uv run python -m r2r.serve &
        echo "Waiting for services to start..."
        sleep 30
