
# Security Policy

At R2R, we take the security of our project and its users seriously. We appreciate the contributions of security researchers and developers in helping us identify and address potential vulnerabilities.

## Reporting a Vulnerability

If you discover a potential security vulnerability in R2R, please follow these steps to report it:

1. Create a new issue on the GitHub repository using the "Vulnerability Disclosure" issue template.
2. Set the issue as "confidential" if you are unsure whether the issue is a potential vulnerability or not. It is easier to make a confidential issue public than to remediate an issue that should have been confidential.
3. Label the issue with the `security` label at a minimum. Additional labels may be applied by the security team and other project maintainers to assist with the triage process.
4. Provide a detailed description of the vulnerability, including steps to reproduce, potential impact, and any other relevant information.
5. If the issue contains sensitive information or user-specific data, such as private repository contents, assign the `keep confidential` label to the issue. If possible, avoid including such information directly in the issue and instead provide links to resources that are only accessible to the project maintainers.

## Vulnerability Handling Process

Once a vulnerability is reported, the R2R security team will follow these steps:

1. Acknowledge receipt of the vulnerability report within 48 hours.
2. Assess the severity and impact of the vulnerability.
3. Develop a fix or mitigation plan for the vulnerability.
4. Notify the reporter about the progress and estimated timeline for the fix.
5. Once the fix is ready, release a new version of R2R that addresses the vulnerability.
6. Publicly disclose the vulnerability and the fix after a reasonable period to allow users to update their installations.

## Scope

This security policy applies to the R2R codebase and its dependencies. It does not cover vulnerabilities in the underlying operating systems, hardware, or third-party libraries used by R2R.

## Recognition

We greatly appreciate the efforts of security researchers and developers who responsibly disclose vulnerabilities to us. With your permission, we will acknowledge your contribution in the release notes and any public disclosures related to the vulnerability.

## Contact

If you have any questions or concerns regarding the security of R2R, please contact the project maintainers at [<EMAIL>](mailto:<EMAIL>).

Thank you for helping us keep R2R and its users secure!
