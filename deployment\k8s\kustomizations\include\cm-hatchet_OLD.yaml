---
# hatchet-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: hatchet-configmap
  annotations:
    argocd.argoproj.io/sync-wave: "-2"
data:
#  DATABASE_POSTGRES_HOST: "hatchet-postgres"
  DATABASE_POSTGRES_HOST: "ferretdb-postgres-documentdb"
  DATABASE_POSTGRES_PORT: "5432"
  SERVER_AUTH_COOKIE_INSECURE: "t"
  SERVER_GRPC_BIND_ADDRESS: "0.0.0.0"
  SERVER_GRPC_BROADCAST_ADDRESS: "hatchet-engine:7077"
  SERVER_GRPC_INSECURE: "t"
  SERVER_AUTH_COOKIE_DOMAIN: "https://r2r.mywebsite.com"
  SERVER_URL: "http://hatchet-dashboard:80"

  HATCHET_DATABASE_POSTGRES_HOST: "ferretdb-postgres-documentdb"
  HATCHET_DATABASE_POSTGRES_PORT: "5432"
  SERVER_GRPC_PORT: "7077"
  SERVER_GRPC_MAX_MSG_SIZE: "134217728"


  HATCHET_DATABASE_POSTGRES_DB_NAME: "hatchet"
  #SERVER_AUTH_COOKIE_DOMAIN: "http://host.docker.internal:${R2R_HATCHET_DASHBOARD_PORT:-7274}"
  #SERVER_URL: "http://host.docker.internal:${R2R_HATCHET_DASHBOARD_PORT:-7274}"
  HATCHET_ADMIN_INIT_ALLOW_OVERRIDE_APIKEY: "false"
  HATCHET_ADMIN_INIT_ALLOW_OVERRIDE_CONF: "false"
  HATCHET_ADMIN_INIT_ALLOW_OVERRIDE_CERT: "false"
  HATCHET_TENANT_ID: "707d0855-80ab-4e1f-a156-f1c4546cbf52"
#  R2R_RABBITMQ_PORT: "5672"
  RABBITMQ_MGMT_PORT: "15672"
  RABBITMQ_URL: "http://hatchet-rabbitmq"

  #New
  HATCHET_CLIENT_TLS_STRATEGY: "none"
  HATCHET_CLIENT_GRPC_MAX_RECV_MESSAGE_LENGTH: "134217728"
  HATCHET_CLIENT_GRPC_MAX_SEND_MESSAGE_LENGTH: "134217728"
