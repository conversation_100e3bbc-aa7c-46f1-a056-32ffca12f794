[build-system]
requires = ["setuptools>=61.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "r2r"
version = "3.6.5"
description = "SciPhi R2R"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "<PERSON>", email = "<EMAIL>"},
]
requires-python = ">=3.10,<3.13"
dependencies = [
    "aiofiles >=24.1.0,<25.0.0",
    "alembic >=1.13.3,<2.0.0",
    "fastapi >=0.115.11,<0.116.0",
    "httpx >=0.27.0",
    "openai >=1.61.0",
    "python-dotenv >=1.0.1,<2.0.0",
    "psycopg-binary >=3.2.3,<4.0.0",
    "requests >=2.31.0,<3.0.0",
    "tiktoken >=0.8.0,<0.9.0",
    "toml >=0.10.2,<0.11.0",
    "types-requests >=2.31.0,<3.0.0",
    "types-aiofiles >=24.1.0.20240626,<25.0.0",
    "typing-extensions >=4.12.2,<5.0.0",
    "pydantic>=2.10.6",
    "python-json-logger>=3.2.1",
    "filetype>=1.2.0",
]

[project.optional-dependencies]
core = [
    "aiohttp >=3.10.10,<4.0.0",
    "aioshutil >=1.5,<2.0",
    "aiosqlite >=0.20.0,<0.21.0",
    "anthropic >=0.49.0",
    "apscheduler >=3.10.4,<4.0.0",
    "asyncpg >=0.29.0,<0.30.0",
    "azure-ai-inference >=1.0.0b8,<2.0.0",
    "azure-ai-ml >=1.24.0,<2.0.0",
    "bcrypt >=4.1.3,<5.0.0",
    "beautifulsoup4 >=4.12.3,<5.0.0",
    "boto3 >=1.35.17,<2.0.0",
    "colorlog >=6.9.0,<7.0.0",
    "docutils >=0.21.2,<0.22.0",
    "epub >=0.5.2,<0.6.0",
    "firecrawl-py >=1.13.5",
    "fsspec >=2024.6.0,<2025.0.0",
    "future >=1.0.0,<2.0.0",
    "google-auth >=2.37.0,<3.0.0",
    "google-auth-oauthlib >=1.2.1,<2.0.0",
    "google-genai >=0.6.0,<0.7.0",
    "gunicorn >=21.2.0,<22.0.0",
    "hatchet-sdk ==0.47.0",
    "litellm >=1.69.3",
    "markdown >=3.6,<4.0",
    "mistralai>=1.5.2",
    "msg-parser>=1.2.0",
    "networkx >=3.3,<4.0",
    "numpy >=1.22.4,<1.29.0",
    "olefile >=0.47,<0.48",
    "ollama >=0.3.1,<0.4.0",
    "openpyxl >=3.1.2,<4.0.0",
    "orgparse >=0.4.20231004,<0.5.0",
    "pdf2image>=1.17.0",
    "pillow >=11.1.0,<12.0.0",
    "pillow-heif >=0.21.0,<0.22.0",
    "psutil >=6.0.0,<7.0.0",
    "pydantic[email] >=2.8.2,<3.0.0",
    "pyjwt >=2.8.0,<3.0.0",
    "pynacl >=1.5.0,<2.0.0",
    "pypdf >=4.2.0,<5.0.0",
    "pypdf2 >=3.0.1,<4.0.0",
    "python-docx >=1.1.0,<2.0.0",
    "python-multipart >=0.0.9,<0.0.19",
    "python-pptx >=1.0.1,<2.0.0",
    "pyyaml >=6.0.1,<7.0.0",
    "sendgrid >=6.11.0,<7.0.0",
    "mailersend >=0.5.6,<0.6.0",
    "sentry-sdk >=2.20.0,<3.0.0",
    "sqlalchemy >=2.0.30,<3.0.0",
    "striprtf >=0.0.28,<0.0.29",
    "supabase >=2.15.0,<3.0.0",
    "tokenizers ==0.19",
    "unstructured-client ==0.34.0",
    "uvicorn >=0.27.0.post1,<0.28.0",
    "vecs >=0.4.0,<0.5.0",
    "xlrd >=2.0.1,<3.0.0",
]

[dependency-groups]
dev = [
    "colorama >=0.4.6,<0.5.0",
    "mypy >=1.5.1,<2.0.0",
    "pre-commit >=2.9,<3.0",
    "pytest >=8.2.0,<9.0.0",
    "pytest-asyncio >=0.23.6,<0.24.0",
    "pytest-dependency >=0.6.0,<0.7.0",
    "pytest-mock >=3.14.0,<4.0.0",
    "pytest-cov>=5.0.0,<6.0.0",
    "pytest-html >=4.1.1,<5.0.0",
    "types-toml >=0.10.8,<0.11.0",
    "pytest-xdist >=3.6.1,<4.0.0",
    "ruff >=0.9.6,<0.10.0",
]
tools = [
    "biopython>=1.85",
    "colorama >=0.4.6,<0.5.0",
    "firecrawl-py>=1.13.5",
    "numpy>=1.26.4",
    "pandas>=2.2.3",
    "scipy>=1.15.2",
    "simpy>=4.1.1",
    "statsmodels>=0.14.4",
]

[project.scripts]
r2r-serve = "r2r.serve:run_server"

[tool.ruff]
exclude = ["py/tests/*"]
line-length = 79
target-version = "py310"
select = ["E", "F", "I", "B"]
ignore = ["B008", "B024", "B026", "E501", "F402", "F403", "F405", "F841"]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
line-ending = "auto"

[tool.mypy]
ignore_missing_imports = true
exclude = 'core/parsers/media/pyzerox/.*|playground/.*|deprecated/.*|dump/.*|docs/source|vecs/*|core/examples/*|sdk/examples/*|tests/*'

[[tool.mypy.overrides]]
module = "yaml"
ignore_missing_imports = true

[tool.pytest.ini_options]
asyncio_mode = "auto"
addopts = "--cov=r2r --cov-report=term-missing --cov-report=xml --cache-clear"
testpaths = [
    "tests",
]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::pytest.PytestUnraisableExceptionWarning",
]

[tool.setuptools]
packages = { find = { where = [ "." ], include = [ "r2r*", "sdk*", "shared*", "core*" ] } }
include-package-data = true

[tool.setuptools.package-data]
core = ["configs/*.toml", "providers/database/prompts/*.yaml"]
r2r = ["r2r.toml"]
