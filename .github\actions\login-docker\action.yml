name: 'Login Docker'
description: 'Sets up Docker for running R2R'
inputs:
  docker_username:
    description: 'Docker Hub username'
    required: true
  docker_password:
    description: 'Docker Hub password or token'
    required: true
runs:
  using: "composite"
  steps:
    - name: Login to Docker Hub
      uses: docker/login-action@v2
      with:
        username: ${{ inputs.docker_username }}
        password: ${{ inputs.docker_password }}
