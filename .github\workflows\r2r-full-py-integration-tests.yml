name: R2R Full Python Integration Test (ubuntu)

on:
  workflow_dispatch:

jobs:
  integration-test:
    runs-on: ubuntu-latest
    timeout-minutes: 30

    env:
      TELEMETRY_ENABLED: 'false'
      R2R_PROJECT_NAME: r2r_default
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      AZURE_API_KEY: ${{ secrets.AZURE_API_KEY }}
      AZURE_API_BASE: ${{ secrets.AZURE_API_BASE }}
      AZURE_API_VERSION: ${{ secrets.AZURE_API_VERSION }}
      PYTHONUNBUFFERED: '1'
      PYTEST_ADDOPTS: '--color=yes'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Python and install dependencies
        uses: ./.github/actions/setup-python-full
        with:
          os: ubuntu-latest
          python-version: '3.12'

      - name: Setup and start Docker
        uses: ./.github/actions/setup-docker
        id: docker-setup

      - name: Login Docker
        uses: ./.github/actions/login-docker
        with:
          docker_username: ${{ secrets.RAGTORICHES_DOCKER_UNAME }}
          docker_password: ${{ secrets.RAGTORICHES_DOCKER_TOKEN }}

      - name: Start R2R Full server
        uses: ./.github/actions/start-r2r-full

      - name: Wait for server to be ready
        run: |
          timeout=300  # 5 minutes timeout
          while ! curl -s http://localhost:7272/health > /dev/null; do
            if [ $timeout -le 0 ]; then
              echo "Server failed to start within timeout"
              exit 1
            fi
            echo "Waiting for server to be ready..."
            sleep 5
            timeout=$((timeout - 5))
          done

      - name: Run R2R Full Python Integration Test
        run: |
          cd py && uv run pytest tests/unit \
            --verbose \
            --capture=no \
            --log-cli-level=INFO
      - name: Run R2R Full Python Integration Test
        run: |
          cd py && uv run pytest tests/integration \
            --verbose \
            --capture=no \
            --log-cli-level=INFO

      - name: Check for test failures
        if: failure()
        run: |
          echo "::error::Integration tests failed. Check the test results artifact for details."
          exit 1

    services:
      redis:
        image: redis:latest
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
